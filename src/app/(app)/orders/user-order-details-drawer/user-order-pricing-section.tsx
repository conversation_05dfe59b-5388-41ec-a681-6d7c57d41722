import { CollectionName } from '@/components/shared/collection-name';
import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { hasSecondaryMarketPrice } from '@/utils/order-utils';

interface UserOrderPricingSectionProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

export function UserOrderPricingSection({
  order,
  collection,
}: UserOrderPricingSectionProps) {
  const hasSecondaryPrice = hasSecondaryMarketPrice(order);

  return (
    <div className="text-center space-y-3">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">
        <CollectionName
          collection={collection}
          fallback={`Collection ${order.collectionId}`}
        />
      </h1>

      {hasSecondaryPrice ? (
        <div className="space-y-3">
          <div>
            <p className="text-[#708499] text-sm mb-1">Primary Price</p>
            <div className="flex items-center justify-center gap-2 p-2 bg-[#232e3c] rounded-lg">
              <TonPriceDisplay
                amount={order.price}
                size={20}
                className="text-xl font-semibold text-[#f5f5f5]"
                showUnit
              />
            </div>
          </div>

          <div>
            <p className="text-[#6ab2f2] text-sm mb-1">
              Secondary Market Price
            </p>
            <div className="flex items-center justify-center gap-2 p-3 bg-[#17212b] rounded-lg border border-[#6ab2f2]/20">
              <TonPriceDisplay
                amount={order.secondaryMarketPrice || 0}
                size={24}
                className="text-3xl font-bold text-[#6ab2f2]"
                showUnit
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center gap-2 p-3 bg-[#17212b] rounded-lg">
          <TonPriceDisplay
            amount={order.price}
            size={24}
            className="text-3xl font-bold text-[#f5f5f5]"
            showUnit
          />
        </div>
      )}
    </div>
  );
}
