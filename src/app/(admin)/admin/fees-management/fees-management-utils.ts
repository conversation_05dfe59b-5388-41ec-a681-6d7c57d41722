import type { FeesFormData } from './fees-management-types';

export const formatBpsToPercent = (bps: number): string => {
  return (bps / 100).toFixed(2);
};

export const formatTonValue = (ton: number): string => {
  return ton.toFixed(4);
};

export const getValidationWarnings = (values: FeesFormData): string[] => {
  const warnings: string[] = [];

  if (values.referrer_fee > values.purchase_fee) {
    warnings.push(
      `Referral fee (${formatBpsToPercent(values.referrer_fee)}%) exceeds purchase fee (${formatBpsToPercent(values.purchase_fee)}%)`,
    );
  }

  if (values.referrer_fee > values.resell_purchase_fee) {
    warnings.push(
      `Referral fee (${formatBpsToPercent(values.referrer_fee)}%) exceeds resell purchase fee (${formatBpsToPercent(values.resell_purchase_fee)}%)`,
    );
  }

  if (values.resell_purchase_fee_for_seller > 5000) {
    warnings.push(
      `Resell fee for seller (${formatBpsToPercent(values.resell_purchase_fee_for_seller)}%) exceeds reasonable limits (50%)`,
    );
  }

  return warnings;
};
