import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  MARKETPLACE_REVENUE_USER_ID,
  MIN_REVENUE_BALANCE_ON_WALLET,
} from "./constants";
import { requireAdminRole } from "./services/auth-middleware";
import {
  hasAvailableBalance,
  updateUserBalance,
} from "./services/balance-service";
import { getTonWalletService } from "./services/ton-wallet-service";
import { UserEntity } from "./types";
import { safeSubtract } from "./utils";
import { log } from "./utils/logger";

export const withdrawRevenue = onCall<{
  withdrawAmount: number;
  johnDowWallet: string;
}>({ cors: true }, async (request) => {
  if (!request.auth) {
    throw new HttpsError(
      "unauthenticated",
      "User must be authenticated to withdraw revenue."
    );
  }

  const { withdrawAmount, johnDowWallet } = request.data;

  if (!withdrawAmount || withdrawAmount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      "Withdrawal amount must be greater than 0."
    );
  }

  if (!johnDowWallet) {
    throw new HttpsError(
      "invalid-argument",
      "John Dow wallet address is required."
    );
  }

  try {
    const db = admin.firestore();
    const userId = request.auth.uid;

    await requireAdminRole(userId);

    const revenueDoc = await db
      .collection("users")
      .doc(MARKETPLACE_REVENUE_USER_ID)
      .get();

    if (!revenueDoc.exists) {
      throw new HttpsError(
        "not-found",
        "Marketplace revenue account not found."
      );
    }

    const revenueUser = {
      id: revenueDoc.id,
      ...revenueDoc.data(),
    } as UserEntity;

    const availableRevenue = revenueUser.balance
      ? safeSubtract(revenueUser.balance.sum, revenueUser.balance.locked)
      : 0;

    if (availableRevenue < MIN_REVENUE_BALANCE_ON_WALLET) {
      throw new HttpsError(
        "failed-precondition",
        "Cannot withdraw when revenue balance is less than 10 TON."
      );
    }

    if (withdrawAmount > safeSubtract(availableRevenue, 10)) {
      throw new HttpsError(
        "failed-precondition",
        `Cannot withdraw more than ${safeSubtract(availableRevenue, 10).toFixed(
          4
        )} TON. Must keep 10 TON minimum.`
      );
    }

    // Check if marketplace revenue has sufficient balance
    const hasBalance = await hasAvailableBalance(
      MARKETPLACE_REVENUE_USER_ID,
      withdrawAmount
    );
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        "Insufficient revenue balance for withdrawal."
      );
    }

    // Send full amount to John Dow wallet

    // Deduct the full amount from marketplace revenue balance
    await updateUserBalance({
      userId: MARKETPLACE_REVENUE_USER_ID,
      sumChange: -withdrawAmount,
      lockedChange: 0,
    });

    // Use TON wallet service to send revenue transfer
    const tonWalletService = getTonWalletService();
    const transferResult = await tonWalletService.sendRevenueTransfer(
      withdrawAmount,
      johnDowWallet
    );

    log.info(
      `Revenue transferred: ${withdrawAmount.toFixed(
        4
      )} TON sent to John Dow wallet`,
      {
        operation: "revenue_withdrawal",
        withdrawAmount,
        johnDowWallet,
        userId,
      }
    );

    return {
      success: transferResult.success,
      message: `Successfully transferred ${withdrawAmount.toFixed(
        4
      )} TON revenue to John Dow`,
      totalAmount: withdrawAmount,
      transactionHash: transferResult.transactionHash,
    };
  } catch (error) {
    log.error("Error in withdrawRevenue function", error, {
      operation: "revenue_withdrawal",
      withdrawAmount,
      johnDowWallet,
      userId: request.auth?.uid,
    });
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError(
      "internal",
      "An error occurred while processing the revenue withdrawal."
    );
  }
});
