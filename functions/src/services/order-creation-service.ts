import * as admin from "firebase-admin";
import {
  CollectionEntity,
  OrderEntity,
  OrderStatus,
  UserType,
  OrderFees,
} from "../types";
import { lockFunds } from "./balance-service";
import { getNextCounterValue } from "./counter-service";
import { validateOrderCreation } from "./order-validation-service";
import { calculateOrderDeadline } from "./deadline-service";
import { getAppConfig } from "./fee-service";

export async function createOrder(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    collectionId: string;
    price: number;
    owned_gift_id: string | null;
    userType: UserType;
    secondaryMarketPrice: number | null;
  }
) {
  const {
    userId,
    collectionId,
    price,
    owned_gift_id,
    userType,
    secondaryMarketPrice,
  } = params;

  const validation = await validateOrderCreation(db, {
    userId,
    collectionId,
    price,
    userType,
  });

  await lockFunds(userId, validation.lockedAmount);

  const orderNumber = await getNextCounterValue("order_number");

  // Get app config to save fees snapshot
  const appConfig = await getAppConfig();
  if (!appConfig) {
    throw new Error("App config not found");
  }

  // Create fees snapshot from current app config
  const fees: OrderFees = {
    buyer_locked_percentage: appConfig.buyer_lock_percentage,
    seller_locked_percentage: appConfig.seller_lock_percentage,
    purchase_fee: appConfig.purchase_fee,
    referrer_fee: appConfig.referrer_fee,
    order_cancellation_fee: appConfig.cancel_order_fee,
    resell_purchase_fee: appConfig.resell_purchase_fee,
    resell_purchase_fee_for_seller: appConfig.resell_purchase_fee_for_seller,
  };

  // Get collection data to calculate deadline
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  const collection = collectionDoc.exists
    ? (collectionDoc.data() as CollectionEntity)
    : null;

  const deadline = calculateOrderDeadline(collection);

  const orderData: Omit<OrderEntity, "id"> = {
    number: orderNumber,
    collectionId,
    price,
    status: OrderStatus.ACTIVE,
    owned_gift_id,
    ...(deadline && { deadline }),
    secondaryMarketPrice,
    reseller_earnings_for_seller: 0,
    fees,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
  };

  if (userType === UserType.BUYER) {
    orderData.buyerId = userId;
  } else {
    orderData.sellerId = userId;
  }

  // Create the order
  const orderRef = await db.collection("orders").add(orderData);

  return {
    success: true,
    orderId: orderRef.id,
    message: `Order created successfully with ${
      validation.lockedAmount
    } TON locked (${validation.lockPercentage * 100}% of ${price} TON order)`,
    lockedAmount: validation.lockedAmount,
    lockPercentage: validation.lockPercentage,
  };
}
