import { TonLogo } from '@/components/TonLogo';

interface TonPriceDisplayProps {
  amount: number;
  size?: number;
  className?: string;
  tonLogoClassName?: string;
  showUnit?: boolean;
}

export function TonPriceDisplay({
  amount,
  size = 24,
  className = '',
  tonLogoClassName = '',
  showUnit = false,
}: TonPriceDisplayProps) {
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <span>{amount}</span>
      <TonLogo size={size} className={tonLogoClassName} />
      {showUnit && <span className="text-sm text-[#708499]">TON</span>}
    </div>
  );
}
