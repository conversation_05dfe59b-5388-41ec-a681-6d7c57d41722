import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { getAppConfig, updateAppConfig } from '@/api/app-config-api';
import { useToast } from '@/hooks/use-toast';

import {
  defaultFeesValues,
  type FeesFormData,
  feesSchema,
} from './fees-management-types';

export const useFeesManagement = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const form = useForm<FeesFormData>({
    resolver: zodResolver(feesSchema),
    defaultValues: defaultFeesValues,
  });

  const loadConfig = async () => {
    try {
      setIsLoadingData(true);
      const config = await getAppConfig();

      if (config) {
        form.reset({
          deposit_fee: config.deposit_fee || defaultFeesValues.deposit_fee,
          withdrawal_fee:
            config.withdrawal_fee || defaultFeesValues.withdrawal_fee,
          referrer_fee: config.referrer_fee || defaultFeesValues.referrer_fee,
          cancel_order_fee:
            config.cancel_order_fee || defaultFeesValues.cancel_order_fee,
          purchase_fee: config.purchase_fee || defaultFeesValues.purchase_fee,
          resell_purchase_fee:
            config.resell_purchase_fee || defaultFeesValues.resell_purchase_fee,
          resell_purchase_fee_for_seller:
            config.resell_purchase_fee_for_seller ||
            defaultFeesValues.resell_purchase_fee_for_seller,
          buyer_lock_percentage:
            config.buyer_lock_percentage ||
            defaultFeesValues.buyer_lock_percentage,
          seller_lock_percentage:
            config.seller_lock_percentage ||
            defaultFeesValues.seller_lock_percentage,
          min_deposit_amount:
            config.min_deposit_amount || defaultFeesValues.min_deposit_amount,
          min_withdrawal_amount:
            config.min_withdrawal_amount ||
            defaultFeesValues.min_withdrawal_amount,
          max_withdrawal_amount:
            config.max_withdrawal_amount ||
            defaultFeesValues.max_withdrawal_amount,
          min_secondary_market_price:
            config.min_secondary_market_price ||
            defaultFeesValues.min_secondary_market_price,
          fixed_cancel_order_fee:
            config.fixed_cancel_order_fee ||
            defaultFeesValues.fixed_cancel_order_fee,
        });
      }
    } catch (error) {
      console.error('Error loading fees config:', error);
      toast({
        title: 'Error',
        description: 'Failed to load current fees configuration.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    loadConfig();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSubmit = async (data: FeesFormData) => {
    setIsLoading(true);

    try {
      await updateAppConfig(data);

      toast({
        title: 'Success',
        description: 'Fees configuration has been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating fees config:', error);
      toast({
        title: 'Error',
        description: 'Failed to update fees configuration. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    isLoading,
    isLoadingData,
    onSubmit,
  };
};
