'use client';

import { createContext, type ReactNode, useContext, useState } from 'react';
import { useDebounceValue } from 'usehooks-ts';

import type { CollectionEntity } from '@/constants/core.constants';

export type SortType = 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';

export interface OrderListFilters {
  minPrice: string;
  maxPrice: string;
  selectedCollection: string;
  sortBy: SortType;
}

export interface OrderListFiltersContextType {
  filters: OrderListFilters;
  debouncedFilters: OrderListFilters;
  collections: CollectionEntity[];
  setMinPrice: (value: string) => void;
  setMaxPrice: (value: string) => void;
  setSelectedCollection: (value: string) => void;
  setSortBy: (value: SortType) => void;
  resetFilters: () => void;
  validatePriceInput: (value: string) => boolean;
}

const OrderListFiltersContext = createContext<
  OrderListFiltersContextType | undefined
>(undefined);

const DEFAULT_FILTERS: OrderListFilters = {
  minPrice: '',
  maxPrice: '',
  selectedCollection: '',
  sortBy: 'date_desc',
};

interface OrderListFiltersProviderProps {
  children: ReactNode;
  collections: CollectionEntity[];
}

export function OrderListFiltersProvider({
  children,
  collections,
}: OrderListFiltersProviderProps) {
  const [filters, setFilters] = useState<OrderListFilters>(DEFAULT_FILTERS);

  // Debounce filters to avoid excessive API calls
  const [debouncedFilters] = useDebounceValue(filters, 500);

  const validatePriceInput = (value: string): boolean => {
    return value === '' || /^\d*\.?\d*$/.test(value);
  };

  const setMinPrice = (value: string) => {
    if (validatePriceInput(value)) {
      setFilters((prev) => ({ ...prev, minPrice: value }));
    }
  };

  const setMaxPrice = (value: string) => {
    if (validatePriceInput(value)) {
      setFilters((prev) => ({ ...prev, maxPrice: value }));
    }
  };

  const setSelectedCollection = (value: string) => {
    setFilters((prev) => ({ ...prev, selectedCollection: value }));
  };

  const setSortBy = (value: SortType) => {
    setFilters((prev) => ({ ...prev, sortBy: value }));
  };

  const resetFilters = () => {
    setFilters(DEFAULT_FILTERS);
  };

  const value: OrderListFiltersContextType = {
    filters,
    debouncedFilters,
    collections,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
    resetFilters,
    validatePriceInput,
  };

  return (
    <OrderListFiltersContext.Provider value={value}>
      {children}
    </OrderListFiltersContext.Provider>
  );
}

export function useOrderListFilters(): OrderListFiltersContextType {
  const context = useContext(OrderListFiltersContext);
  if (context === undefined) {
    throw new Error(
      'useOrderListFilters must be used within an OrderListFiltersProvider',
    );
  }
  return context;
}
