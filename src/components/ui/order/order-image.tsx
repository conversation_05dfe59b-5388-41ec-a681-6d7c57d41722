import { TgsOrImage } from '@/components/TgsOrImage';
import type { CollectionEntity } from '@/constants/core.constants';

interface OrderImageProps {
  collection: CollectionEntity | null;
  className?: string;
  aspectRatio?: string;
  showHoverEffect?: boolean;
  isAnimated?: boolean;
}

export function OrderImage({
  isAnimated,
  collection,
  className = '',
  aspectRatio = 'aspect-[1/1.2]',
  showHoverEffect = true,
}: OrderImageProps) {
  const hoverClass = showHoverEffect
    ? 'group-hover:scale-105 transition-transform duration-200'
    : '';

  return (
    <div
      className={`pt-6 relative rounded-lg overflow-hidden bg-[#17212b] ${aspectRatio} ${className}`}
    >
      {collection ? (
        <TgsOrImage
          isImage={!isAnimated}
          collectionId={collection.id}
          imageProps={{
            alt: collection.name || 'Order item',
            fill: true,
            className: `object-cover p-4 mt-2 ${hoverClass}`,
          }}
          tgsProps={{
            style: { height: 'auto', width: 'auto', padding: '8px' },
          }}
        />
      ) : (
        <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
          <div className="w-6 h-6 bg-[#17212b] rounded" />
        </div>
      )}
    </div>
  );
}
