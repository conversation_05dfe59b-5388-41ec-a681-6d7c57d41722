import { AlertTriangle } from 'lucide-react';

import type { OrderEntity } from '@/constants/core.constants';

interface ResellPriceWarningProps {
  order: OrderEntity;
  priceValue: number;
  className?: string;
}

export function ResellPriceWarning({
  order,
  priceValue,
  className = '',
}: ResellPriceWarningProps) {
  const calculateLockedCollateral = () => {
    if (!order?.fees) return 0;

    const buyerLockPercentage = order.fees.buyer_locked_percentage / 10000;
    const sellerLockPercentage = order.fees.seller_locked_percentage / 10000;

    const buyerLocked = order.price * buyerLockPercentage;
    const sellerLocked = order.price * sellerLockPercentage;

    return buyerLocked + sellerLocked;
  };

  const totalLockedCollateral = calculateLockedCollateral();
  const isHighRiskPrice =
    priceValue > 0 && priceValue > totalLockedCollateral * 2;

  if (!isHighRiskPrice) {
    return null;
  }

  return (
    <div
      className={`bg-red-900/20 rounded-xl p-4 border border-red-500/30 ${className}`}
    >
      <div className="flex items-start gap-3">
        <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
        <div className="space-y-1">
          <p className="text-sm font-medium text-red-400">High Risk Warning</p>
          <p className="text-xs text-red-300 leading-relaxed">
            Be careful with setting this price ({priceValue.toFixed(2)} TON),
            which is much higher than the locked collateral (
            {totalLockedCollateral.toFixed(2)} TON) in this order. You could be
            scammed by fraudulent users who manipulate order prices from
            multiple wallets.
          </p>
        </div>
      </div>
    </div>
  );
}
