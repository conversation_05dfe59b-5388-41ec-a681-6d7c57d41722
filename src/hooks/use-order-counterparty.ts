import { useEffect, useState } from 'react';

import { getUserById } from '@/api/auth-api';
import type {
  OrderEntity,
  UserEntity,
  UserType,
} from '@/constants/core.constants';
import { getOtherUserId } from '@/utils/order-utils';

interface UseOrderCounterpartyProps {
  order: OrderEntity | null;
  userType: UserType;
  isOpen: boolean;
}

export function useOrderCounterparty({
  order,
  userType,
  isOpen,
}: UseOrderCounterpartyProps) {
  const [otherUser, setOtherUser] = useState<UserEntity | null>(null);
  const [loadingUser, setLoadingUser] = useState(false);

  useEffect(() => {
    const fetchOtherUser = async () => {
      if (!order) return;

      const otherUserId = getOtherUserId(order, userType);
      if (!otherUserId) return;

      setLoadingUser(true);
      try {
        const user = await getUserById(otherUserId);
        setOtherUser(user);
      } catch (error) {
        console.error('Error fetching user:', error);
        setOtherUser(null);
      } finally {
        setLoadingUser(false);
      }
    };

    if (isOpen) {
      fetchOtherUser();
    } else {
      setOtherUser(null);
      setLoadingUser(false);
    }
  }, [order, userType, isOpen]);

  return { otherUser, loadingUser };
}
