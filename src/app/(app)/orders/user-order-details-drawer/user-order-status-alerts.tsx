import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock, ExternalLink, Gift } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import type { OrderEntity } from '@/constants/core.constants';
import { TELEGRAM_BOT_URL, UserType } from '@/constants/core.constants';
import {
  shouldShowFreezeWarning,
  shouldShowGiftReadySection,
  shouldShowGiftRefundSection,
} from '@/utils/order-utils';

interface UserOrderStatusAlertsProps {
  order: OrderEntity;
  userType: UserType;
  isFreezed: boolean;
}

interface StatusAlertProps {
  variant: 'warning' | 'info' | 'success' | 'gradient' | 'refund';
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

const variantStyles = {
  warning: {
    container: 'bg-yellow-500/10 border border-yellow-500/20',
    icon: 'text-yellow-400',
    title: 'text-yellow-400',
  },
  info: {
    container: 'bg-blue-500/10 border border-blue-500/20',
    icon: 'text-blue-400',
    title: 'text-blue-400',
  },
  success: {
    container: 'bg-green-500/10 border border-green-500/20',
    icon: 'text-green-400',
    title: 'text-green-400',
  },
  gradient: {
    container:
      'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20',
    icon: 'text-purple-400',
    title: 'text-purple-400',
  },
  refund: {
    container: 'bg-orange-500/10 border border-orange-500/20',
    icon: 'text-orange-400',
    title: 'text-orange-400',
  },
};

const buttonStyles = {
  gradient: 'bg-purple-500 hover:bg-purple-600',
  refund: 'bg-orange-500 hover:bg-orange-600',
};

function StatusAlert({
  variant,
  icon: Icon,
  title,
  description,
  buttonText,
  onButtonClick,
}: StatusAlertProps) {
  const styles = variantStyles[variant];

  return (
    <div className={`${styles.container} rounded-2xl p-4`}>
      <div className="flex items-center gap-2 mb-2">
        <Icon className={`w-5 h-5 ${styles.icon}`} />
        <span className={`${styles.title} font-semibold`}>{title}</span>
      </div>
      <p className="text-[#708499] text-sm">{description}</p>
      {buttonText && onButtonClick && (
        <Button
          size="sm"
          className={`w-full ${buttonStyles[variant as keyof typeof buttonStyles]} text-white rounded-xl`}
          onClick={onButtonClick}
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          {buttonText}
        </Button>
      )}
    </div>
  );
}

export function UserOrderStatusAlerts({
  order,
  userType,
  isFreezed,
}: UserOrderStatusAlertsProps) {
  const showFreezeWarning = shouldShowFreezeWarning(order, userType, isFreezed);
  const showGiftReady = shouldShowGiftReadySection(order, userType);
  const showGiftRefund = shouldShowGiftRefundSection(order, userType);

  const alerts = [
    {
      show: showFreezeWarning,
      props: {
        variant: 'warning' as const,
        icon: AlertTriangle,
        title: 'Freeze Period Active',
        description:
          'Collection items cannot be transferred yet. Wait for the freeze period to end.',
      },
    },
    {
      show: !isFreezed && userType === UserType.SELLER && !order.deadline,
      props: {
        variant: 'info' as const,
        icon: Clock,
        title: 'Waiting for Transfer',
        description: 'Wait until the collection item becomes transferable.',
      },
    },
    {
      show: !isFreezed && userType === UserType.SELLER && order.deadline,
      props: {
        variant: 'success' as const,
        icon: Gift,
        title: 'Ready to Send',
        description: 'You can now send the gift to the relayer.',
      },
    },
    {
      show: showGiftReady,
      props: {
        variant: 'gradient' as const,
        icon: Gift,
        title: 'Gift Ready!',
        description:
          'Your gift has been sent to the relayer. Please visit the bot to claim your gift.',
        buttonText: 'Open Bot to Claim',
        onButtonClick: () => window.open(TELEGRAM_BOT_URL, '_blank'),
      },
    },
    {
      show: showGiftRefund,
      props: {
        variant: 'refund' as const,
        icon: AlertTriangle,
        title: 'Gift Refund Available',
        description: 'Go to the relayer to refund your gift.',
        buttonText: 'Open Bot for Refund',
        onButtonClick: () => window.open(TELEGRAM_BOT_URL, '_blank'),
      },
    },
  ];

  const visibleAlerts = alerts.filter((alert) => alert.show);

  if (visibleAlerts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {visibleAlerts.map((alert, index) => (
        <StatusAlert key={index} {...alert.props} />
      ))}
    </div>
  );
}
