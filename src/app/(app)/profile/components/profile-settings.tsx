'use client';

import { useLocalStorage } from 'usehooks-ts';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { LocalStorageKeys } from '@/constants/storage.constants';

export const ProfileSettings = () => {
  const [isAnimatedCollection, setIsAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <label
                htmlFor="animated-collection"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Animated Collections
              </label>
              <p className="text-xs text-[#708499]">
                Enable animated collection previews and effects
              </p>
            </div>
            <Switch
              id="animated-collection"
              checked={isAnimatedCollection}
              onCheckedChange={setIsAnimatedCollection}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
