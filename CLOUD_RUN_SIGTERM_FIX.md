# Cloud Run SIGTERM Issue Fix

## Problem
The Node.js Telegram bot was receiving SIGTERM signals from Cloud Run and shutting down unexpectedly. This was causing service interruptions and container restarts.

## Root Causes Identified

1. **Port Mismatch**: Production environment was configured to use port 3001, but Cloud Run expected port 8080
2. **Slow Graceful Shutdown**: The shutdown process was taking too long (>10 seconds), causing Cloud Run to send SIGKILL
3. **Inefficient Connection Handling**: HTTP server wasn't properly closing active connections during shutdown
4. **Webhook Cleanup Delay**: Attempting to clean up webhooks during shutdown was adding unnecessary delay

## Fixes Implemented

### 1. Port Configuration Fix
- **File**: `.env.production`
- **Change**: Updated `PORT=3001` to `PORT=8080`
- **File**: `src/services/http-server.ts`
- **Change**: Updated default port from 8081 to 8080

### 2. Improved Graceful Shutdown
- **File**: `src/index.ts`
- **Changes**:
  - Added 8-second timeout for graceful shutdown (leaving 2-second buffer before Cloud Run SIGKILL)
  - Optimized shutdown order: stop healthcheck → stop bot → stop HTTP server → disconnect Redis
  - Added timeout for Redis disconnect (3 seconds max)
  - Removed webhook cleanup during shutdown (webhook re-established on restart)

### 3. Enhanced HTTP Server Shutdown
- **File**: `src/services/http-server.ts`
- **Changes**:
  - Added connection tracking for active HTTP connections
  - Implemented immediate connection termination during shutdown
  - Added readiness probe endpoint (`/readiness`) for better Cloud Run integration

### 4. Periodic Health Monitoring
- **File**: `src/services/healthcheck.ts`
- **Changes**:
  - Added periodic healthcheck updates every 5 minutes
  - Proper cleanup of intervals during shutdown

### 5. Better Startup Logging
- **File**: `src/index.ts`
- **Changes**:
  - Added detailed startup configuration logging
  - Added process PID logging for debugging
  - Added readiness state management

## New Endpoints

### `/readiness`
- **Purpose**: Cloud Run startup probe endpoint
- **Response**: 200 when ready, 503 when not ready
- **Usage**: Helps Cloud Run determine when container is ready to receive traffic

### Enhanced `/healthcheck`
- **Purpose**: Liveness probe with Redis connectivity check
- **Response**: Includes last healthcheck timestamp and health status

## Testing

### Local Testing
```bash
# Test graceful shutdown locally
./scripts/test-graceful-shutdown.sh
```

### Cloud Run Deployment
1. Deploy with updated configuration
2. Monitor logs for startup messages
3. Verify readiness endpoint: `curl https://your-service.run.app/readiness`
4. Monitor for SIGTERM issues in Cloud Run logs

## Expected Results

1. **Faster Startup**: Readiness probe helps Cloud Run route traffic only when ready
2. **Graceful Shutdown**: Complete shutdown within 8 seconds
3. **No More SIGTERM Issues**: Proper signal handling prevents unexpected shutdowns
4. **Better Monitoring**: Enhanced logging and health checks for debugging

## Monitoring

Watch for these log messages:
- `🚦 Server readiness: READY` - Server is ready to receive traffic
- `🛑 Received SIGTERM, shutting down gracefully...` - Graceful shutdown initiated
- `✅ Graceful shutdown completed` - Shutdown completed successfully
- `⚠️ Graceful shutdown timeout, forcing exit` - Shutdown took too long (investigate)

## Cloud Run Configuration Recommendations

Consider adding these environment variables in Cloud Run:
```
PORT=8080
NODE_ENV=production
```

And configure health checks:
- **Startup probe**: `/readiness`
- **Liveness probe**: `/healthcheck`
- **Readiness probe**: `/readiness`
