'use client';

import { CheckCir<PERSON>, Clock, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface CountdownPopupProps {
  show: boolean;
  onClose: () => void;
  onComplete: () => void;
  initialSeconds?: number;
  title?: string;
  message?: string;
  onInterval?: (currentTime: number) => void;
}

export function CountdownPopup({
  show,
  onClose,
  onComplete,
  initialSeconds = 60,
  title = 'Deposit Processing',
  message = 'You will receive your funds within',
  onInterval,
}: CountdownPopupProps) {
  const [countdown, setCountdown] = useState(initialSeconds);

  useEffect(() => {
    if (show) {
      setCountdown(initialSeconds);
    }
  }, [show, initialSeconds]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (show && countdown > 0) {
      interval = setInterval(() => {
        setCountdown((prev) => {
          const newTime = prev - 1;
          onInterval?.(newTime);
          return newTime;
        });
      }, 1000);
    } else if (countdown === 0 && show) {
      onComplete();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [show, countdown, onComplete, onInterval]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!show) return null;

  return (
    <div className="fixed bottom-18 left-4 right-4 max-w-sm mx-auto">
      <div className="bg-[#17212b] border border-[#3a4a5c]/30 rounded-lg shadow-lg p-4 animate-in slide-in-from-bottom-2 duration-300">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <CheckCircle className="w-6 h-6 text-green-400" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-medium text-[#f5f5f5]">{title}</h3>
              <div className="flex items-center gap-1 text-xs text-[#708499]">
                <Clock className="w-3 h-3" />
              </div>
            </div>
            <p className="text-sm text-[#708499]">
              {message} {formatTime(countdown)} minutes.
            </p>
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 text-[#708499] hover:text-[#f5f5f5] transition-colors"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
