import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getCountFromServer,
  getDocs,
  limit,
  or,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import {
  AppCloudFunctions,
  type OrderEntity,
  ORDERS_COLLECTION_NAME,
} from '@/constants/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export const getPaidOrdersCount = async () => {
  try {
    const q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('status', '==', 'paid'),
    );

    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting paid orders count:', error);
    throw error;
  }
};

export interface OrderStats {
  totalOrders: number;
  paidOrdersWithoutSecondaryPrice: number;
  paidOrdersWithSecondaryPrice: number;
  giftSentToRelayerOrders: number;
  fulfilledOrders: number;
  cancelledOrders: number;
}

export const getOrderStats = async () => {
  try {
    const ordersCollection = collection(firestore, ORDERS_COLLECTION_NAME);

    const [
      totalOrdersSnapshot,
      giftSentToRelayerSnapshot,
      fulfilledOrdersSnapshot,
      cancelledOrdersSnapshot,
    ] = await Promise.all([
      getCountFromServer(ordersCollection),
      getCountFromServer(
        query(ordersCollection, where('status', '==', 'gift_sent_to_relayer')),
      ),
      getCountFromServer(
        query(ordersCollection, where('status', '==', 'fulfilled')),
      ),
      getCountFromServer(
        query(ordersCollection, where('status', '==', 'cancelled')),
      ),
    ]);

    const paidOrdersQuery = query(
      ordersCollection,
      where('status', '==', 'paid'),
    );
    const paidOrdersDocs = await getDocs(paidOrdersQuery);

    let paidOrdersWithoutSecondaryPrice = 0;
    let paidOrdersWithSecondaryPrice = 0;

    paidOrdersDocs.docs.forEach((doc) => {
      const orderData = doc.data();
      if (
        orderData.secondaryMarketPrice !== null &&
        orderData.secondaryMarketPrice !== undefined
      ) {
        paidOrdersWithSecondaryPrice++;
      } else {
        paidOrdersWithoutSecondaryPrice++;
      }
    });

    const stats: OrderStats = {
      totalOrders: totalOrdersSnapshot.data().count,
      paidOrdersWithoutSecondaryPrice,
      paidOrdersWithSecondaryPrice,
      giftSentToRelayerOrders: giftSentToRelayerSnapshot.data().count,
      fulfilledOrders: fulfilledOrdersSnapshot.data().count,
      cancelledOrders: cancelledOrdersSnapshot.data().count,
    };

    return stats;
  } catch (error) {
    console.error('Error getting order statistics:', error);
    throw error;
  }
};

export const getUserOrders = async (userId: string) => {
  try {
    const buyerOrdersQuery = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('buyerId', '==', userId),
    );

    const sellerOrdersQuery = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('sellerId', '==', userId),
    );

    const [buyerSnapshot, sellerSnapshot] = await Promise.all([
      getDocs(buyerOrdersQuery),
      getDocs(sellerOrdersQuery),
    ]);

    const orders: OrderEntity[] = [];

    buyerSnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    sellerSnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    const uniqueOrders = orders.filter(
      (order, index, self) =>
        index === self.findIndex((o) => o.id === order.id),
    );

    const statusPriority = {
      gift_sent_to_relayer: 1,
      paid: 2,
      active: 3,
      cancelled: 4,
      fulfilled: 5,
    };

    uniqueOrders.sort((a, b) => {
      const aPriority =
        statusPriority[a.status as keyof typeof statusPriority] || 999;
      const bPriority =
        statusPriority[b.status as keyof typeof statusPriority] || 999;

      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }

      const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return bDate - aDate;
    });

    return uniqueOrders;
  } catch (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
};

export const getUserOrdersPaginated = async (
  userId: string,
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    // Use compound query to get both buyer and seller orders
    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(where('buyerId', '==', userId), where('sellerId', '==', userId)),
    );

    // Sort by status priority first, then by creation date
    q = query(q, orderBy('createdAt', 'desc'));

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    const hasMore = docs.length > pageSize;
    const ordersToReturn = hasMore ? docs.slice(0, pageSize) : docs;

    const orders: OrderEntity[] = ordersToReturn.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    // Apply client-side sorting by status priority
    const statusPriority = {
      gift_sent_to_relayer: 1,
      paid: 2,
      active: 3,
      cancelled: 4,
      fulfilled: 5,
    };

    orders.sort((a, b) => {
      const aPriority =
        statusPriority[a.status as keyof typeof statusPriority] || 999;
      const bPriority =
        statusPriority[b.status as keyof typeof statusPriority] || 999;

      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }

      const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return bDate - aDate;
    });

    const lastDoc = hasMore ? ordersToReturn[ordersToReturn.length - 1] : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching user orders paginated:', error);
    throw error;
  }
};

export interface SetSecondaryMarketPriceResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
}

export interface CancelOrderResponse {
  success: boolean;
  message: string;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

export const setSecondaryMarketPrice = async (
  orderId: string,
  secondaryMarketPrice: number,
) => {
  try {
    const setSecondaryMarketPriceFunction = httpsCallable<
      { orderId: string; secondaryMarketPrice: number },
      SetSecondaryMarketPriceResponse
    >(firebaseFunctions, AppCloudFunctions.setSecondaryMarketPrice);

    const result = await setSecondaryMarketPriceFunction({
      orderId,
      secondaryMarketPrice,
    });

    return result.data;
  } catch (error) {
    console.error('Error setting secondary market price:', error);
    throw error;
  }
};

export const cancelOrder = async (orderId: string, userId: string) => {
  try {
    const cancelOrderFunction = httpsCallable<
      { orderId: string; userId: string },
      CancelOrderResponse
    >(firebaseFunctions, AppCloudFunctions.cancelUserOrder);

    const result = await cancelOrderFunction({
      orderId,
      userId,
    });

    return result.data;
  } catch (error) {
    console.error('Error cancelling order:', error);
    throw error;
  }
};

export const getEligibleOrdersForResale = async () => {
  try {
    const ordersQuery = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('buyerId', '==', getCurrentUserId()),
      where('status', '==', 'paid'),
      where('sellerId', '!=', null),
      where('secondaryMarketPrice', '==', null),
    );

    const snapshot = await getDocs(ordersQuery);
    const orders: OrderEntity[] = [];

    snapshot.forEach((doc) => {
      const orderData = doc.data() as OrderEntity;
      if (!orderData.secondaryMarketPrice) {
        orders.push({ id: doc.id, ...orderData });
      }
    });

    orders.sort((a, b) => {
      const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return bDate - aDate;
    });

    return orders;
  } catch (error) {
    console.error('Error fetching eligible orders for resale:', error);
    throw error;
  }
};
