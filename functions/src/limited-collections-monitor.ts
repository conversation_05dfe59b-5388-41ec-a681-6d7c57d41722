import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import { CollectionEntity, CollectionStatus } from "./types";
import {
  getTelegramBotToken,
  getTelegramApiId,
  getTelegramApiHash,
} from "./config";
import { addDeadlineToOrders as addDeadlineToOrdersService } from "./services/deadline-service";
import { log } from "./utils/logger";

const db = admin.firestore();

interface LimitedGift {
  id: string;
  limited: boolean;
  upgradeStars: number | null;
}

async function fetchLimitedCollections() {
  const botToken = getTelegramBotToken();
  const apiId = getTelegramApiId();
  const apiHash = getTelegramApiHash();

  const stringSession = new StringSession("");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    await client.start({
      botAuthToken: botToken,
    });

    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    // Handle the result properly based on its type
    if (!("gifts" in result)) {
      log.monitorLog("No gifts found in result", {
        monitor: "limited_collections",
        status: "no_gifts",
      });
      return [];
    }

    const limitedGifts = (result as any).gifts
      .filter((gift: any) => gift.limited === true)
      .map((gift: any) => ({
        id: gift.id.toString(),
        limited: gift.limited,
        upgradeStars: gift.upgradeStars ? gift.upgradeStars.toString() : null,
      }));

    log.monitorLog("Found limited gifts from Telegram API", {
      monitor: "limited_collections",
      count: limitedGifts.length,
    });
    return limitedGifts;
  } catch (error) {
    log.error("Error fetching limited collections from Telegram", error, {
      monitor: "limited_collections",
      operation: "fetch_telegram_api",
    });
    throw error;
  } finally {
    await client.disconnect();
  }
}

async function updateCollectionToMarket(collectionId: string) {
  const collectionRef = db.collection("collections").doc(collectionId);

  await collectionRef.update({
    status: CollectionStatus.MARKET,
    launchedAt: admin.firestore.Timestamp.now(),
  });

  log.monitorLog("Updated collection to MARKET status", {
    monitor: "limited_collections",
    collectionId,
    status: "market_updated",
  });
}

async function addDeadlineToOrders(collectionId: string) {
  await addDeadlineToOrdersService(db, collectionId);
}

async function createNewCollection(gift: LimitedGift) {
  const newCollection: CollectionEntity = {
    id: gift.id,
    name: "Limited Gift",
    description: "Limited collection",
    status: CollectionStatus.PREMARKET,
    floorPrice: 0.1, // Default floor price in TON
    active: true, // New collections are active by default
  };

  await db.collection("collections").doc(gift.id).set(newCollection);
  log.monitorLog("Created new collection in Firestore", {
    monitor: "limited_collections",
    collectionId: gift.id,
    status: "collection_created",
  });
}

async function processUpgradeableCollection(gift: LimitedGift) {
  try {
    const collectionRef = db.collection("collections").doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      log.monitorLog(
        "Collection not found in Firestore, creating new collection",
        {
          monitor: "limited_collections",
          collectionId: gift.id,
          status: "creating_collection",
        }
      );
      await createNewCollection(gift);

      // After creating, we need to get the collection data to continue processing
      const newCollectionDoc = await collectionRef.get();
      if (!newCollectionDoc.exists) {
        log.error("Failed to create collection", undefined, {
          monitor: "limited_collections",
          collectionId: gift.id,
          operation: "collection_creation",
        });
        return;
      }
    }

    // Get the collection data (either existing or newly created)
    const updatedCollectionDoc = await collectionRef.get();
    const collection = updatedCollectionDoc.data() as CollectionEntity;

    // Skip if not PREMARKET status
    if (collection.status !== CollectionStatus.PREMARKET) {
      log.monitorLog("Collection status is not PREMARKET, skipping", {
        monitor: "limited_collections",
        collectionId: gift.id,
        currentStatus: collection.status,
        status: "skipping",
      });
      return;
    }

    log.monitorLog("Processing collection with upgradeStars", {
      monitor: "limited_collections",
      collectionId: gift.id,
      upgradeStars: gift.upgradeStars,
      status: "processing",
    });

    // Update collection status to MARKET and set launchedAt
    await updateCollectionToMarket(gift.id);

    // Add deadlines to orders without them
    await addDeadlineToOrders(gift.id);

    log.monitorLog("Successfully processed collection", {
      monitor: "limited_collections",
      collectionId: gift.id,
      status: "completed",
    });
  } catch (error) {
    log.error("Error processing collection", error, {
      monitor: "limited_collections",
      collectionId: gift.id,
      operation: "process_upgradeable_collection",
    });
    throw error;
  }
}

async function ensureCollectionExists(gift: LimitedGift) {
  try {
    const collectionRef = db.collection("collections").doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      log.monitorLog(
        "Collection not found in Firestore, creating new collection",
        {
          monitor: "limited_collections",
          collectionId: gift.id,
          status: "creating_new",
        }
      );
      await createNewCollection(gift);
    } else {
      log.monitorLog("Collection already exists in Firestore", {
        monitor: "limited_collections",
        collectionId: gift.id,
        status: "already_exists",
      });
    }
  } catch (error) {
    log.error("Error ensuring collection exists", error, {
      monitor: "limited_collections",
      collectionId: gift.id,
      operation: "ensure_collection_exists",
    });
    throw error;
  }
}

export async function checkLimitedCollections() {
  try {
    log.monitorLog("Starting limited collections check", {
      monitor: "limited_collections",
      status: "started",
    });

    const limitedGifts = await fetchLimitedCollections();

    if (limitedGifts.length === 0) {
      log.monitorLog("No limited collections found from Telegram API", {
        monitor: "limited_collections",
        status: "no_collections",
      });
      return;
    }

    log.monitorLog("Found limited collections from Telegram API", {
      monitor: "limited_collections",
      count: limitedGifts.length,
    });

    // First, ensure all limited collections exist in Firestore
    for (const gift of limitedGifts) {
      try {
        await ensureCollectionExists(gift);
      } catch (error) {
        log.error("Failed to ensure collection exists", error, {
          monitor: "limited_collections",
          collectionId: gift.id,
          operation: "ensure_collection_batch",
        });
        // Continue processing other collections even if one fails
      }
    }

    // Then, process upgradeable collections (those with upgradeStars)
    const upgradeableGifts = limitedGifts.filter(
      // @ts-expect-error: upgradeStars is not null
      (gift) => gift.upgradeStars !== null
    );

    if (upgradeableGifts.length === 0) {
      log.monitorLog("No upgradeable limited collections found", {
        monitor: "limited_collections",
        status: "no_upgradeable",
      });
      return;
    }

    log.monitorLog("Found upgradeable limited collections", {
      monitor: "limited_collections",
      count: upgradeableGifts.length,
      status: "found_upgradeable",
    });

    for (const gift of upgradeableGifts) {
      try {
        await processUpgradeableCollection(gift);
      } catch (error) {
        log.error("Failed to process collection", error, {
          monitor: "limited_collections",
          collectionId: gift.id,
          operation: "process_collection_batch",
        });
      }
    }

    log.monitorLog("Limited collections check completed", {
      monitor: "limited_collections",
      status: "completed",
    });
  } catch (error) {
    log.error("Error in checkLimitedCollections", error, {
      monitor: "limited_collections",
      operation: "check_limited_collections",
    });
    throw error;
  }
}

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 1 * * *", // Run daily at 1 AM UTC
    timeZone: "UTC",
  },
  async () => {
    try {
      log.monitorLog("Limited collections monitor triggered", {
        monitor: "limited_collections",
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await checkLimitedCollections();
      log.monitorLog("Limited collections monitor completed successfully", {
        monitor: "limited_collections",
        status: "completed",
      });
    } catch (error) {
      log.error("Limited collections monitor failed", error, {
        monitor: "limited_collections",
        status: "monitor_failed",
      });
    }
  }
);
