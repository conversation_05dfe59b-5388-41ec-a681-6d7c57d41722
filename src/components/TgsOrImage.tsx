'use client';

import dynamic from 'next/dynamic';

import { useGiftImagePath } from '@/hooks/use-gift-image-path';

const TgsViewer = dynamic(
  () => import('./TgsViewer').then((mod) => ({ default: mod.TgsViewer })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-300"></div>
      </div>
    ),
  },
);

interface TgsOrImageProps {
  isImage: boolean;
  collectionId: string;
  imageProps?: {
    alt: string;
    fill?: boolean;
    className?: string;
    loading?: 'lazy' | 'eager';
    sizes?: string;
    onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  };
  tgsProps?: {
    style?: React.CSSProperties;
    className?: string;
  };
}

export function TgsOrImage({
  isImage,
  collectionId,
  imageProps,
  tgsProps,
}: TgsOrImageProps) {
  const imagePath = useGiftImagePath({ collectionId, format: 'png' });

  const tgsPath = useGiftImagePath({ collectionId, format: 'tgs' });

  if (!collectionId) {
    return null;
  }

  if (isImage && imageProps) {
    if (!imagePath.src) {
      return null;
    }

    // Note better to use simple image here
    return (
      <img
        src={imagePath.src}
        alt={imageProps.alt}
        // fill={imageProps.fill}
        className={imageProps.className}
        loading={imageProps.loading}
        sizes={imageProps.sizes}
        onError={(e) => {
          imagePath.onError();
          imageProps.onError?.(e);
        }}
      />
    );
  }

  if (!isImage && tgsProps) {
    return (
      <TgsViewer
        tgsUrl={tgsPath.src}
        style={tgsProps.style}
        onError={tgsPath.onError}
        className={tgsProps.className}
      />
    );
  }

  return null;
}
