'use client';

import { Input as TgInput, Select } from '@telegram-apps/telegram-ui';
import { useLocalStorage } from 'usehooks-ts';

import { CollectionSelect } from '@/components/ui/collection-select';
import { LocalStorageKeys } from '@/constants/storage.constants';
import { useOrderListFilters } from '@/contexts/OrderListFiltersContext';

export const MarketplaceFilters = () => {
  const {
    filters,
    collections,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
  } = useOrderListFilters();
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMinPrice(value);
  };

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMaxPrice(value);
  };

  return (
    <div className="flex flex-wrap items-end gap-2 py-3 rounded-lg">
      <div className="flex-1 min-w-[130px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header="Min"
            placeholder="0"
            value={filters.minPrice}
            onChange={handleMinPriceChange}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]! [&+h6]:max-w-[100px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[130px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header="Max"
            placeholder="0"
            value={filters.maxPrice}
            onChange={handleMaxPriceChange}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[130px] mt-2">
        <CollectionSelect
          animated={isAnimatedCollection}
          collections={collections}
          value={filters.selectedCollection}
          onValueChange={setSelectedCollection}
          placeholder="All Collections"
        />
      </div>

      <div className="flex-1 min-w-[130px] mt-2">
        <div className="[&>div]:p-0!">
          <Select
            header="Sort by"
            value={filters.sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="[&>select]:px-[12px]! [&>select]:py-[6px]! [&+h6]:-top-[12px]!"
          >
            <option value="date_desc">Newest First</option>
            <option value="date_asc">Oldest First</option>
            <option value="price_desc">Price: High to Low</option>
            <option value="price_asc">Price: Low to High</option>
          </Select>
        </div>
      </div>
    </div>
  );
};
