import type { Control, FieldPath } from 'react-hook-form';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import type { FeeFieldConfig, FeesFormData } from './fees-management-types';
import { formatBpsToPercent, formatTonValue } from './fees-management-utils';

interface FeesFormFieldProps {
  control: Control<FeesFormData>;
  config: FeeFieldConfig;
}

export const FeesFormField = ({ control, config }: FeesFormFieldProps) => {
  const { name, label, placeholder, description, type } = config;

  return (
    <FormField
      control={control}
      name={name as FieldPath<FeesFormData>}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              type="text"
              placeholder={placeholder}
              className="border-2 border-gray-300 focus:border-blue-500"
              {...field}
              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
            />
          </FormControl>
          <FormDescription>
            {description}. Current:{' '}
            {type === 'ton'
              ? `${formatTonValue(field.value)} TON`
              : `${formatBpsToPercent(field.value)}%`}
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
