import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';

interface OrderDetailsHeaderSectionProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

export function OrderDetailsHeaderSection({
  collection,
}: OrderDetailsHeaderSectionProps) {
  return (
    <div className="text-center space-y-2">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">
        {collection?.name || 'Unknown Collection'}
      </h1>
    </div>
  );
}
