import { GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
} from 'firebase/firestore';

import type { UserEntity } from '@/constants/core.constants';
import { firebaseAuth, firestore } from '@/root-context';

export const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();

  try {
    await signInWithPopup(firebaseAuth, provider);
  } catch (error) {
    console.error('Error during sign in:', error);
  }
};

export const signOut = async () => {
  try {
    await firebaseAuth.signOut();
  } catch (error) {
    console.error('Error during sign out:', error);
  }
};

export const getCurrentUserId = () => firebaseAuth.currentUser?.uid;

export const getUserRole = async () => {
  try {
    const q = query(
      collection(firestore, 'users'),
      where('id', '==', getCurrentUserId()),
    );

    const adminSnapshot = await getDocs(q);

    const admins: UserEntity[] = [];
    adminSnapshot.forEach((doc) => {
      admins.push({ id: doc.id, ...doc.data() });
    });
    return admins[0]?.role;
  } catch (error) {
    console.error('Error checking if current User is admin:', error);
    throw error;
  }
};

export const getUserById = async (userId?: string) => {
  try {
    if (!userId) {
      throw new Error('No user ID provided and no current user found');
    }

    const userDocRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      console.warn(`User with ID ${userId} not found`);
      return null;
    }

    const userData = userDoc.data();
    return {
      id: userDoc.id,
      ...userData,
    } as UserEntity;
  } catch (error) {
    console.error(`Error getting user by ID ${userId}:`, error);
    throw error;
  }
};
