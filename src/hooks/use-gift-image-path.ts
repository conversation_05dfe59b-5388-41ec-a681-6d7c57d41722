'use client';

import { useEffect, useState } from 'react';

import { getImagePathWithFallback } from '@/utils/image-path';

interface UseImagePathOptions {
  collectionId: string;
  format?: 'png' | 'tgs';
}

interface UseImagePathResult {
  src: string;
  isLoading: boolean;
  isCdn: boolean;
  error: boolean;
  onError: () => void;
}

export const useGiftImagePath = ({
  collectionId,
  format = 'png',
}: UseImagePathOptions): UseImagePathResult => {
  const [src, setSrc] = useState<string>('');
  const [isCdn, setIsCdn] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    if (!collectionId) {
      setSrc('');
      setIsCdn(false);
      setError(false);
      return;
    }

    setError(false);

    const { primary } = getImagePathWithFallback(collectionId, format);

    setSrc(primary);
    setIsCdn(true);
  }, [collectionId, format]);

  const handleError = () => {
    if (isCdn) {
      const { fallback } = getImagePathWithFallback(collectionId, format);
      setSrc(fallback);
      setIsCdn(false);
      setError(false);
    } else {
      setError(true);
    }
  };

  return {
    src,
    isLoading: false,
    isCdn,
    error,
    onError: handleError,
  };
};
