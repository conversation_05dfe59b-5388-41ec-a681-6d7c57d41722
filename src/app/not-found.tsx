'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function NotFound() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to root path immediately when a 404 occurs
    router.push('/');
  }, [router]);

  // Show a brief loading message while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Redirecting...</h2>
        <p className="text-gray-400">Taking you back to the marketplace</p>
      </div>
    </div>
  );
}
