import { z } from 'zod';

export const feesSchema = z.object({
  deposit_fee: z.number().min(0).max(10), // Static TON value
  withdrawal_fee: z.number().min(0).max(10), // Static TON value
  referrer_fee: z.number().min(0).max(10000), // BPS
  cancel_order_fee: z.number().min(0).max(10000), // BPS
  purchase_fee: z.number().min(0).max(10000), // BPS
  resell_purchase_fee: z.number().min(0).max(10000), // BPS
  resell_purchase_fee_for_seller: z.number().min(0).max(10000), // BPS
  buyer_lock_percentage: z.number().min(0).max(10000), // BPS
  seller_lock_percentage: z.number().min(0).max(10000), // BPS
  min_deposit_amount: z.number().min(0).max(1000), // Static TON value
  min_withdrawal_amount: z.number().min(0).max(1000), // Static TON value
  max_withdrawal_amount: z.number().min(0).max(10000), // Static TON value
  min_secondary_market_price: z.number().min(0).max(1000), // Static TON value
  fixed_cancel_order_fee: z.number().min(0).max(10), // Static TON value
});

export type FeesFormData = z.infer<typeof feesSchema>;

export const defaultFeesValues: FeesFormData = {
  deposit_fee: 0.1, // 0.1 TON default
  withdrawal_fee: 0.1, // 0.1 TON default
  referrer_fee: 200, // 2% BPS
  cancel_order_fee: 150, // 1.5% BPS
  purchase_fee: 750, // 7.5% BPS
  resell_purchase_fee: 500, // 5% BPS default
  resell_purchase_fee_for_seller: 200, // 2% BPS default
  buyer_lock_percentage: 10000, // 100% BPS default
  seller_lock_percentage: 2000, // 20% BPS default
  min_deposit_amount: 0.1, // 0.1 TON default
  min_withdrawal_amount: 1, // 1 TON default
  max_withdrawal_amount: 100, // 100 TON default
  min_secondary_market_price: 1, // 1 TON default
  fixed_cancel_order_fee: 0.1, // 0.1 TON default
};

export interface FeeFieldConfig {
  name: keyof FeesFormData;
  label: string;
  placeholder: string;
  description: string;
  type: 'ton' | 'bps';
}

export const feeFieldConfigs: FeeFieldConfig[] = [
  {
    name: 'deposit_fee',
    label: 'Deposit Fee (TON)',
    placeholder: '0.1',
    description: 'Static fee deducted from deposits',
    type: 'ton',
  },
  {
    name: 'withdrawal_fee',
    label: 'Withdraw Fee (TON)',
    placeholder: '0.1',
    description: 'Static fee deducted from withdrawals',
    type: 'ton',
  },
  {
    name: 'fixed_cancel_order_fee',
    label: 'Fixed Cancel Order Fee (TON)',
    placeholder: '0.1',
    description: 'Static fee applied when cancelling single-person orders',
    type: 'ton',
  },
  {
    name: 'min_withdrawal_amount',
    label: 'Minimum Withdrawal Amount (TON)',
    placeholder: '1',
    description: 'Minimum amount users can withdraw',
    type: 'ton',
  },
  {
    name: 'max_withdrawal_amount',
    label: 'Maximum Withdrawal Amount (TON)',
    placeholder: '100',
    description: 'Maximum amount users can withdraw',
    type: 'ton',
  },
  {
    name: 'min_secondary_market_price',
    label: 'Minimum Secondary Market Price (TON)',
    placeholder: '1',
    description: 'Minimum price for reselling orders on secondary market',
    type: 'ton',
  },
  {
    name: 'purchase_fee',
    label: 'Purchase Fee (BPS)',
    placeholder: '750',
    description: 'Total fee applied to purchases',
    type: 'bps',
  },
  {
    name: 'referrer_fee',
    label: 'Referral Fee (BPS)',
    placeholder: '200',
    description: 'Fee paid to referrers (part of purchase fee)',
    type: 'bps',
  },
  {
    name: 'cancel_order_fee',
    label: 'Cancel Order Fee (BPS)',
    placeholder: '150',
    description: 'Percentage fee for cancelling two-person orders',
    type: 'bps',
  },
  {
    name: 'resell_purchase_fee',
    label: 'Resell Purchase Fee (BPS)',
    placeholder: '500',
    description: 'Fee applied to secondary market purchases',
    type: 'bps',
  },
  {
    name: 'resell_purchase_fee_for_seller',
    label: 'Resell Fee for Seller (BPS)',
    placeholder: '200',
    description: 'Fee paid to original seller on resell',
    type: 'bps',
  },
  {
    name: 'buyer_lock_percentage',
    label: 'Buyer Lock Percentage (BPS)',
    placeholder: '10000',
    description: 'Percentage of order amount locked for buyers',
    type: 'bps',
  },
  {
    name: 'seller_lock_percentage',
    label: 'Seller Lock Percentage (BPS)',
    placeholder: '2000',
    description: 'Percentage of order amount locked for sellers',
    type: 'bps',
  },
  {
    name: 'min_deposit_amount',
    label: 'Minimum Deposit Amount (TON)',
    placeholder: '0.1',
    description: 'Minimum amount users can deposit',
    type: 'ton',
  },
];
