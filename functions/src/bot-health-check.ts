import { onSchedule } from "firebase-functions/v2/scheduler";
import fetch from "node-fetch";
import { BOT_HEALTH_CHECK_ENDPOINT } from "./constants";
import { log } from "./utils/logger";

export async function checkBotHealth() {
  try {
    log.monitorLog("Starting bot health check", {
      monitor: "bot_health",
      status: "started",
    });

    const botAppUrl = process.env.BOT_APP_URL;
    const bearerToken = process.env.HEALTH_CHECK_BEARER_TOKEN;

    if (!botAppUrl) {
      throw new Error("BOT_APP_URL environment variable is not configured");
    }

    if (!bearerToken) {
      throw new Error(
        "HEALTH_CHECK_BEARER_TOKEN environment variable is not configured"
      );
    }

    const healthCheckUrl = `${botAppUrl}${BOT_HEALTH_CHECK_ENDPOINT}`;
    log.monitorLog("Calling bot health check endpoint with authentication", {
      monitor: "bot_health",
      healthCheckUrl,
      hasAuthentication: true,
    });

    const response = await fetch(healthCheckUrl, {
      method: "GET",
      timeout: 30000, // 30 second timeout
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        "Content-Type": "application/json",
        "User-Agent": "marketplace-functions/health-check",
      },
    });

    if (!response.ok) {
      throw new Error(
        `Bot health check failed with status ${response.status}: ${response.statusText}`
      );
    }

    const responseData = await response.json();
    log.monitorLog("Bot health check response received", {
      monitor: "bot_health",
      responseData,
    });

    if (responseData.status === "healthy") {
      log.monitorLog("✅ Bot health check passed - bot is healthy", {
        monitor: "bot_health",
        status: "healthy",
      });
    } else {
      log.warn("⚠️ Bot health check returned unhealthy status", {
        monitor: "bot_health",
        status: "unhealthy",
        responseData,
      });
    }

    return {
      success: true,
      status: responseData.status,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error("❌ Bot health check failed", error, {
      monitor: "bot_health",
      status: "failed",
    });
    throw error;
  }
}

export const botHealthCheck = onSchedule(
  {
    schedule: "*/15 * * * *", // Every 15 minutes
    timeZone: "UTC",
  },
  async () => {
    try {
      log.monitorLog("Bot health check monitor triggered", {
        monitor: "bot_health",
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await checkBotHealth();
      log.monitorLog("Bot health check monitor completed successfully", {
        monitor: "bot_health",
        status: "completed",
      });
    } catch (error) {
      log.error("Bot health check monitor failed", error, {
        monitor: "bot_health",
        status: "monitor_failed",
      });
    }
  }
);
