'use client';

import { useTonConnectUI } from '@tonconnect/ui-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { TonConnectButton } from '@/components/shared/ton-connect-button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTelegramAuth } from '@/hooks/use-telegram-auth';
import { useRootContext } from '@/root-context';

export const ProfileWalletSection = () => {
  const [tonConnectUI] = useTonConnectUI();
  const { currentUser } = useRootContext();
  const { authenticate } = useTelegramAuth();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const formatAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const handleWalletAction = async () => {
    if (!currentUser) {
      setIsAuthenticating(true);
      try {
        await authenticate();
      } catch (error) {
        console.error('Authentication failed:', error);
        setIsAuthenticating(false);
        return;
      }
      setIsAuthenticating(false);
    }

    if (tonConnectUI.account?.address) {
      return 'show-dropdown';
    } else {
      setIsConnecting(true);
      try {
        await tonConnectUI.openModal();
      } catch (error) {
        console.error('Error opening wallet modal:', error);
        setIsConnecting(false);
      }
    }
  };

  const handleReconnectWallet = async () => {
    try {
      await tonConnectUI.disconnect();
      toast.success('Wallet disconnected');

      // Small delay to ensure disconnection is complete
      setTimeout(async () => {
        try {
          setIsConnecting(true);

          await tonConnectUI.openModal();
        } catch (error) {
          console.error('Error opening wallet modal:', error);
          setIsConnecting(false);
        }
      }, 500);
    } catch (error) {
      console.error('Error reconnecting wallet:', error);
      toast.error('Failed to reconnect wallet');
    }
  };

  const handleDisconnectWallet = async () => {
    try {
      await tonConnectUI.disconnect();
      toast.success('Wallet disconnected');
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
      toast.error('Failed to disconnect wallet');
    }
  };

  const tonWalletAddress = tonConnectUI.account?.address || '';

  return (
    <Card>
      <CardHeader>
        <CardTitle>TON Wallet</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {tonWalletAddress ? (
              <div>
                <p className="text-sm text-[#708499]">Connected Wallet</p>
                <p className="text-lg font-mono">
                  {formatAddress(tonWalletAddress)}
                </p>
              </div>
            ) : (
              <div>
                <p className="text-sm text-[#708499]">No wallet connected</p>
                <p className="text-lg">
                  Connect your TON wallet to start trading
                </p>
              </div>
            )}
          </div>

          <div className="flex gap-2">
            <TonConnectButton
              tonWalletAddress={tonWalletAddress}
              isConnecting={isConnecting}
              isAuthenticating={isAuthenticating}
              onWalletAction={handleWalletAction}
              onDisconnectWallet={handleDisconnectWallet}
              formatAddress={formatAddress}
              size="compact"
              showDropdown={false}
              className="min-w-[100px]"
            />

            {tonWalletAddress && (
              <button
                onClick={handleReconnectWallet}
                disabled={isConnecting}
                className="px-3 py-2 text-sm bg-[#3a4a5c] hover:bg-[#4a5a6c] text-white rounded-full border border-[#4a5a6c] transition-colors disabled:opacity-50"
              >
                {isConnecting ? 'Connecting...' : 'Reconnect'}
              </button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
