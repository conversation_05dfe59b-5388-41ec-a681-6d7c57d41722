// TonConnect manifest URL using dynamic API route
const getManifestUrl = () => {
  if (typeof window === 'undefined') {
    // Server-side: use production URL as fallback
    return 'https://marketplace-ui-blush.vercel.app/api/tonconnect-manifest';
  }

  // Client-side: use dynamic API route that adapts to current origin
  return `${window.location.origin}/api/tonconnect-manifest`;
};

export const WALLET_MANIFEST_URL = getManifestUrl();

// Debug logging for development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  console.log('TonConnect Manifest URL:', WALLET_MANIFEST_URL);
}
