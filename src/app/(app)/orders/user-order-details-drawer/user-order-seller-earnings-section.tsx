import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import type { OrderEntity } from '@/constants/core.constants';

interface UserOrderSellerEarningsSectionProps {
  order: OrderEntity;
}

export function UserOrderSellerEarningsSection({
  order,
}: UserOrderSellerEarningsSectionProps) {
  if (
    !order.reseller_earnings_for_seller ||
    order.reseller_earnings_for_seller <= 0
  ) {
    return null;
  }

  return (
    <div className="pt-6 border-t border-[#3a4a5c]/30">
      <h3 className="text-lg font-semibold text-[#f5f5f5] mb-3">
        Resale Earnings
      </h3>
      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <span className="text-green-400 text-sm font-medium">
            Total earnings from resales:
          </span>
          <TonPriceDisplay
            amount={order.reseller_earnings_for_seller || 0}
            size={20}
            className="text-xl font-bold text-green-400"
            showUnit
          />
        </div>
        <p className="text-xs text-green-300/80 mt-2">
          You will receive your earnings from resell when the order is
          fulfilled. This amount represents your accumulated earnings from each
          time this order is resold on the secondary market.
        </p>
      </div>
    </div>
  );
}
