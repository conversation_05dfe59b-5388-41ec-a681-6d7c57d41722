import { Alert<PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Gift, XCircle } from 'lucide-react';

import { OrderStatus } from '@/constants/core.constants';

interface OrderStatusIconProps {
  status: OrderStatus;
  className?: string;
}

export function OrderStatusIcon({
  status,
  className = 'w-4 h-4',
}: OrderStatusIconProps) {
  switch (status) {
    case OrderStatus.ACTIVE:
      return <Clock className={`${className} text-blue-400`} />;
    case OrderStatus.PAID:
      return <AlertTriangle className={`${className} text-yellow-400`} />;
    case OrderStatus.GIFT_SENT_TO_RELAYER:
      return <Gift className={`${className} text-purple-400`} />;
    case OrderStatus.FULFILLED:
      return <CheckCircle className={`${className} text-green-400`} />;
    case OrderStatus.CANCELLED:
      return <XCircle className={`${className} text-red-400`} />;
    default:
      return null;
  }
}
