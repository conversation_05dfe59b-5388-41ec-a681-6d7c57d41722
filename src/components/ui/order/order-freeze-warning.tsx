import { AlertTriangle } from 'lucide-react';

import type {
  CollectionEntity,
  OrderEntity,
  UserType,
} from '@/constants/core.constants';
import {
  calculateFreezeEndDate,
  formatFreezeEndDate,
  shouldShowFreezeWarning,
} from '@/utils/order-utils';

interface OrderFreezeWarningProps {
  order: OrderEntity;
  userType: UserType;
  isFreezed: boolean;
  collection: CollectionEntity | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function OrderFreezeWarning({
  order,
  userType,
  isFreezed,
  collection,
  className = '',
  size = 'sm',
}: OrderFreezeWarningProps) {
  if (!shouldShowFreezeWarning(order, userType, isFreezed)) {
    return null;
  }

  const freezeEndDate = calculateFreezeEndDate(collection);

  const sizeClasses = {
    sm: {
      container: 'p-1.5 text-[10px]',
      icon: 'w-2.5 h-2.5',
      text: 'text-[9px]',
    },
    md: {
      container: 'p-3 text-sm',
      icon: 'w-4 h-4',
      text: 'text-xs',
    },
    lg: {
      container: 'p-4 text-base',
      icon: 'w-5 h-5',
      text: 'text-sm',
    },
  };

  const classes = sizeClasses[size];

  return (
    <div
      className={`bg-yellow-500/10 border border-yellow-500/20 rounded ${classes.container} ${className}`}
    >
      <div className="flex items-center gap-1 mb-0.5">
        <AlertTriangle className={`${classes.icon} text-yellow-400`} />
        <span className="text-yellow-400">Freeze period</span>
      </div>
      {freezeEndDate && (
        <div className={`text-[#708499] ${classes.text}`}>
          Ends: {formatFreezeEndDate(freezeEndDate)}
        </div>
      )}
    </div>
  );
}
