'use client';

import { useEffect, useState } from 'react';

import { FREEZE_PERIOD_DAYS } from '@/constants/core.constants';

interface FreezePeriodStatusProps {
  launchedAt?: Date | null;
}

interface FreezePeriodInfo {
  status: 'not_started' | 'active' | 'ended';
  label: string;
  timeRemaining?: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
}

function calculateFreezePeriodInfo(launchedAt?: Date | null): FreezePeriodInfo {
  if (!launchedAt) {
    return {
      status: 'not_started',
      label: "Freeze period hasn't started yet",
    };
  }

  const now = new Date();
  const launchDate = new Date(launchedAt);
  const freezeEndDate = new Date(
    launchDate.getTime() + FREEZE_PERIOD_DAYS * 24 * 60 * 60 * 1000,
  );

  if (now >= freezeEndDate) {
    return {
      status: 'ended',
      label: 'Freeze period has ended',
    };
  }

  // Calculate time remaining
  const timeDiff = freezeEndDate.getTime() - now.getTime();
  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

  return {
    status: 'active',
    label: `${days}d ${hours}h ${minutes}m ${seconds}s remaining`,
    timeRemaining: { days, hours, minutes, seconds },
  };
}

export function FreezePeriodStatus({ launchedAt }: FreezePeriodStatusProps) {
  const [freezeInfo, setFreezeInfo] = useState<FreezePeriodInfo>(() =>
    calculateFreezePeriodInfo(launchedAt),
  );

  useEffect(() => {
    const updateFreezeInfo = () => {
      setFreezeInfo(calculateFreezePeriodInfo(launchedAt));
    };

    // Update immediately
    updateFreezeInfo();

    // Update every second for active countdowns
    let interval: NodeJS.Timeout | null = null;
    if (freezeInfo.status === 'active') {
      interval = setInterval(updateFreezeInfo, 1000); // Update every second
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [launchedAt, freezeInfo.status]);

  const getStatusColor = () => {
    switch (freezeInfo.status) {
      case 'not_started':
        return 'text-gray-500';
      case 'active':
        return 'text-orange-500';
      case 'ended':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <span className={`text-sm ${getStatusColor()}`}>{freezeInfo.label}</span>
  );
}
