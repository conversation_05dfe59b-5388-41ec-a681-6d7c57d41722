import { useCallback } from 'react';

import { deleteOrder } from '@/api/admin-api';
import { cancelOrder } from '@/api/order-api';
import { useToast } from '@/hooks/use-toast';
import { useRootContext } from '@/root-context';

export function useAdminOrdersActions() {
  const { toast } = useToast();
  const { currentUser } = useRootContext();

  const handleCancelOrder = useCallback(
    async (orderId: string) => {
      if (!currentUser?.id) return;

      try {
        await cancelOrder(orderId, currentUser.id);
        toast({
          title: 'Success',
          description: 'Order cancelled successfully',
        });
      } catch (error) {
        console.error('Error cancelling order:', error);
        toast({
          title: 'Error',
          description: 'Failed to cancel order',
          variant: 'destructive',
        });
        throw error;
      }
    },
    [currentUser?.id, toast],
  );

  const handleDeleteOrder = useCallback(
    async (orderId: string) => {
      try {
        await deleteOrder(orderId);
        toast({
          title: 'Success',
          description: 'Order deleted successfully',
        });
      } catch (error) {
        console.error('Error deleting order:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete order',
          variant: 'destructive',
        });
        throw error;
      }
    },
    [toast],
  );

  return {
    handleCancelOrder,
    handleDeleteOrder,
  };
}
