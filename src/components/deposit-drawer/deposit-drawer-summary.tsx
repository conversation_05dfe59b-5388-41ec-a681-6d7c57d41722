import { TonLogo } from '@/components/TonLogo';

interface DepositDrawerSummaryProps {
  depositAmount: string;
  depositFee: number;
}

export function DepositDrawerSummary({
  depositAmount,
  depositFee,
}: DepositDrawerSummaryProps) {
  const totalAmount = parseFloat(depositAmount) + depositFee;

  return (
    <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-[#708499]">Deposit amount:</span>
          <div className="flex items-center gap-1">
            <span className="text-[#6ab2f2] font-semibold">
              {depositAmount}
            </span>
            <TonLogo size={24} />
          </div>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-[#708499]">Deposit fee:</span>
          <div className="flex items-center gap-1">
            <span className="text-[#6ab2f2] font-semibold">{depositFee}</span>
            <TonLogo size={24} />
          </div>
        </div>
        <div className="flex justify-between items-center font-medium border-t border-[#3a4a5c]/30 pt-3">
          <span className="text-[#f5f5f5]">Total to pay:</span>
          <div className="flex items-center gap-1">
            <span className="text-[#6ab2f2] font-semibold">
              {totalAmount.toFixed(1)}
            </span>
            <TonLogo size={24} />
          </div>
        </div>
      </div>
    </div>
  );
}
