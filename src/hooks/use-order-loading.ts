import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useRef, useState } from 'react';

import type { OrderEntity } from '@/constants/core.constants';
import type { SortType } from '@/contexts/OrderListFiltersContext';

interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy: SortType;
  currentUserId?: string;
  limit?: number;
}

interface OrderState {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
}

interface UseOrderLoadingProps {
  loadOrdersFunction: (
    filters: OrderFilters & { lastDoc?: DocumentSnapshot | null },
  ) => Promise<{
    orders: OrderEntity[];
    lastDoc: DocumentSnapshot | null;
    hasMore: boolean;
  }>;
  filters: OrderFilters;
}

interface UseOrderLoadingReturn {
  state: OrderState;
  loadOrders: (reset?: boolean) => Promise<void>;
  loadMoreOrders: () => void;
  resetOrders: () => void;
}

const createInitialState = (): OrderState => ({
  orders: [],
  loading: false,
  loadingMore: false,
  hasMore: true,
});

const addOrdersWithoutDuplicates = (
  existingOrders: OrderEntity[],
  newOrders: OrderEntity[],
  context: string,
): OrderEntity[] => {
  const existingIds = new Set(existingOrders.map((order) => order.id));
  const filteredNewOrders = newOrders.filter(
    (order) => !existingIds.has(order.id),
  );
  const duplicates = newOrders.filter((order) => existingIds.has(order.id));

  if (duplicates.length > 0) {
    console.warn(
      `🚨 Found duplicate orders for ${context}:`,
      duplicates.map((o) => o.id),
    );
  }

  return [...existingOrders, ...filteredNewOrders];
};

const validateSortBy = (sortBy: SortType): SortType => {
  return typeof sortBy === 'string' &&
    ['price_asc', 'price_desc', 'date_asc', 'date_desc'].includes(sortBy)
    ? sortBy
    : 'date_desc';
};

export const useOrderLoading = ({
  loadOrdersFunction,
  filters,
}: UseOrderLoadingProps): UseOrderLoadingReturn => {
  const [state, setState] = useState<OrderState>(createInitialState);
  const lastDocRef = useRef<DocumentSnapshot | null>(null);

  const loadOrders = useCallback(
    async (reset = true) => {
      if (reset) {
        setState((prev) => ({
          ...prev,
          loading: true,
          orders: [],
          hasMore: true,
        }));
        lastDocRef.current = null;
      } else {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      try {
        const validSortBy = validateSortBy(filters.sortBy);
        const requestFilters = {
          ...filters,
          sortBy: validSortBy,
          limit: filters.limit || 3,
          lastDoc: reset ? null : lastDocRef.current,
        };

        const result = await loadOrdersFunction(requestFilters);

        setState((prev) => {
          if (reset) {
            return {
              ...prev,
              orders: result.orders,
              hasMore: result.hasMore,
            };
          } else {
            return {
              ...prev,
              orders: addOrdersWithoutDuplicates(
                prev.orders,
                result.orders,
                'order loading',
              ),
              hasMore: result.hasMore,
            };
          }
        });

        lastDocRef.current = result.lastDoc;
      } catch (error) {
        console.error('Error loading orders:', error);
      } finally {
        setState((prev) => ({
          ...prev,
          loading: false,
          loadingMore: false,
        }));
      }
    },
    [loadOrdersFunction, filters],
  );

  const loadMoreOrders = useCallback(() => {
    const isLoading = state.loading || state.loadingMore;

    if (state.hasMore && !isLoading) {
      loadOrders(false);
    }
  }, [state.hasMore, state.loading, state.loadingMore, loadOrders]);

  const resetOrders = useCallback(() => {
    setState(createInitialState());
    lastDocRef.current = null;
  }, []);

  return {
    state,
    loadOrders,
    loadMoreOrders,
    resetOrders,
  };
};
