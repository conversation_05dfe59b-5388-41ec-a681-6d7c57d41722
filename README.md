# Marketplace Bot

A simple Telegram bot for the marketplace platform that handles order management and gift echo functionality.

## Features

- **Order Management**: View and complete marketplace orders
- **Gift Echo**: Simple gift echo system using Telegram Bot API transferGift method
- **Referral System**: Generate and share referral links
- **Business Account Integration**: Uses Telegram Business Account for gift transfers
- **Cloud Function Session Storage**: Persistent session management using Firebase Cloud Functions for scalability
- **Webhook Support**: Automatic webhook setup for Cloud Run deployment with SIGTERM handling
- **Health Monitoring**: Built-in health checks and monitoring endpoints

## Setup

1. **Install Dependencies**

   ```bash
   npm install
   ```

2. **Redis Setup**

   Install and start Redis server:

   ```bash
   # macOS (using Homebrew)
   brew install redis
   brew services start redis

   # Ubuntu/Debian
   sudo apt update
   sudo apt install redis-server
   sudo systemctl start redis-server

   # Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

3. **Environment Configuration**

   The bot uses separate environment files for development and production:

   **Development Environment** (`.env.development`):

   ```env
   # Development Environment Configuration
   NODE_ENV=development

   # Bot Configuration
   BOT_TOKEN=your_development_bot_token
   WEB_APP_URL=https://your-dev-app.vercel.app/
   WEBHOOK_URL=https://your-dev-webhook-url

   # Server Configuration
   PORT=3001
   LOG_LEVEL=debug

   # Firebase Configuration (development project)
   FIREBASE_PROJECT_ID=your-dev-project-id
   FIREBASE_REGION=us-central1

   # Firebase Configuration for session storage
   FIREBASE_PROJECT_ID=your-dev-project-id
   FIREBASE_REGION=us-central1
   ```

   **Production Environment** (`.env.production`):

   ```env
   # Production Environment Configuration
   NODE_ENV=production

   # Bot Configuration
   BOT_TOKEN=your_production_bot_token
   WEB_APP_URL=https://your-prod-app.vercel.app/
   WEBHOOK_URL=https://your-prod-webhook-url

   # Server Configuration
   PORT=3001
   LOG_LEVEL=info

   # Firebase Configuration (production project)
   FIREBASE_PROJECT_ID=your-prod-project-id
   FIREBASE_REGION=us-central1

   # Redis Configuration
   REDIS_HOST_LOCAL=localhost
   REDIS_PORT_LOCAL=6379
   ```

4. **Get Bot Token**

   - Message [@BotFather](https://t.me/botfather) on Telegram
   - Create a new bot with `/newbot`
   - Copy the bot token to your `.env` file

5. **Set Up Business Account**

   - Connect your bot to a Telegram Business Account
   - The business connection ID will be automatically provided in gift messages

6. **Start the Bot**

   ```bash
   # Development mode (polling)
   npm run dev

   # Production mode (webhook)
   npm run build
   npm start
   ```

## Cloud Run Deployment

For production deployment on Google Cloud Run with webhook support:

1. **Build and Deploy**

   ```bash
   npm run build
   gcloud run deploy marketplace-bot --source .
   ```

2. **Set Environment Variables**

   ```bash
   gcloud run services update marketplace-bot \
     --set-env-vars NODE_ENV=production \
     --set-env-vars WEBHOOK_URL=https://your-service-url.run.app \
     --set-env-vars BOT_TOKEN=your_bot_token
   ```

3. **Setup Webhook**
   ```bash
   npm run setup:webhook
   ```

### SIGTERM Handling

The bot automatically handles Cloud Run container restarts:

- ✅ **Webhook Auto-Setup**: Automatically configures webhook on startup
- ✅ **Graceful Shutdown**: Cleans up webhook on SIGTERM/SIGINT
- ✅ **Error Recovery**: Comprehensive error handling and logging
- ✅ **Health Monitoring**: Built-in health checks at `/healthcheck`

See [WEBHOOK_SETUP.md](WEBHOOK_SETUP.md) for detailed troubleshooting.

## Bot Commands

### User Commands

- `/start` - Start the bot and show main menu
- `/help` - Show help information

## How It Works

### Order Management

1. Users can view their marketplace orders using "📋 Get My Orders"
2. For paid orders, users can complete them by sending the item/gift to the bot
3. The bot automatically processes the completion and updates the marketplace

### Gift Echo System

1. User sends a gift directly to the business account (not to the bot)
2. Bot receives a `business_message` update containing the gift information
3. The bot logs the gift data (sender, gift ID, business connection ID, timestamp)
4. The bot fetches owned gifts from the business account using `getBusinessAccountGifts`
5. The bot transfers one of the owned gifts to the sender using the `transferGift` API
6. Simple and efficient - uses official Bot API business account integration

### Referral System

Users can generate referral links to earn commissions on marketplace transactions.

## Architecture

- **Bot Handler** (`src/bot.ts`): Main bot configuration with simple gift echo logic
- **Command Handlers** (`src/handlers/`): Handle different types of user interactions
- **Utilities** (`src/utils/`): Keyboard layouts and helper functions

## Development

### Available Scripts

- **Development Mode**: `npm run dev` (uses `.env.development`, auto-reload)
- **Production Mode**: `npm start` (uses `.env.production`)
- **Build**: `npm run build`
- **Testing**:
  - `npm run test:webhook` (development)
  - `npm run test:webhook:prod` (production)
  - `npm run test:healthcheck` (development)
  - `npm run test:healthcheck:prod` (production)

### Environment Management

The bot automatically loads the appropriate environment file based on `NODE_ENV`:

- `NODE_ENV=development` → loads `.env.development`
- `NODE_ENV=production` → loads `.env.production`

No more manual environment variable switching needed!

## Gift Echo Implementation

The gift echo functionality is implemented using middleware to catch business message updates:

```typescript
bot.use(async (ctx, next) => {
  try {
    // Handle business_connection updates
    if (ctx.update && "business_connection" in ctx.update) {
      const businessConnection = (ctx.update as any).business_connection;
      const businessAccountId = businessConnection.user_chat_id;
      console.log(`Business Account Connected: ID = ${businessAccountId}`);
      return;
    }

    // Handle business_message updates (including gifts)
    if (ctx.update && "business_message" in ctx.update) {
      const businessMessage = (ctx.update as any).business_message;

      if (businessMessage && "gift" in businessMessage) {
        const gift = businessMessage.gift;
        const senderId = businessMessage.from?.id;
        const businessConnectionId = businessMessage.business_connection_id;

        console.log("Gift received in business account:", {
          giftId: gift?.id,
          senderId,
          businessConnectionId,
          date: new Date(),
        });

        if (senderId && gift?.id && businessConnectionId) {
          // Get owned gifts from the business account
          const ownedGiftsResponse = await fetch(
            `https://api.telegram.org/bot${BOT_TOKEN}/getBusinessAccountGifts`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                business_connection_id: businessConnectionId,
                limit: 10,
              }),
            }
          );

          const ownedGiftsResult = await ownedGiftsResponse.json();

          if (
            ownedGiftsResult.ok &&
            ownedGiftsResult.result?.gifts?.length > 0
          ) {
            const ownedGift = ownedGiftsResult.result.gifts[0];

            await fetch(
              `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
              {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                  business_connection_id: businessConnectionId,
                  owned_gift_id: ownedGift.id,
                  new_owner_chat_id: senderId,
                }),
              }
            );

            console.log(`Gift ${ownedGift.id} sent to user ${senderId}`);
          } else {
            console.log("No owned gifts found in business account");
          }
        }
        return;
      }
    }

    await next(); // Continue for regular updates
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
});
```

## Security Notes

- Keep your bot token secure
- Ensure proper business account permissions
- Monitor gift transfer activity through logs

## Troubleshooting

### Common Issues

1. **Gift transfers not working**:

   - Verify the bot is connected to a Telegram Business Account
   - Check that the business account has the necessary permissions
   - Ensure the bot receives `business_message` updates (check webhook/polling settings)
   - Verify the business account owns the gift being transferred

2. **Bot not responding**:
   - Check if the bot token is correct
   - Verify the bot is running
   - Check console logs for errors
