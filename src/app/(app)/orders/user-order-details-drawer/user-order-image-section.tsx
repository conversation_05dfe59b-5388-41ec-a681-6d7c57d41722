import { TgsOrImage } from '@/components/TgsOrImage';
import type { CollectionEntity } from '@/constants/core.constants';

interface UserOrderImageSectionProps {
  collection: CollectionEntity | null;
}

export function UserOrderImageSection({
  collection,
}: UserOrderImageSectionProps) {
  return (
    <div className="relative">
      <div className="aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] p-8 border border-[#3a4a5c]/50">
        {collection ? (
          <TgsOrImage
            isImage={false}
            collectionId={collection.id}
            imageProps={{
              alt: collection.name || 'Order item',
              fill: true,
              className: 'object-contain drop-shadow-2xl',
            }}
            tgsProps={{
              style: { height: '100%', width: '100%' },
            }}
          />
        ) : (
          <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
            <div className="w-16 h-16 bg-[#17212b] rounded" />
          </div>
        )}
      </div>
    </div>
  );
}
