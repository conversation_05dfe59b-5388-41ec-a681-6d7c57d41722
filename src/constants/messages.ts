export const MESSAGES = {
  TELEGRAM_ID_ERROR:
    "❌ Unable to identify your Telegram ID. Please try again.",
  ORDERS: {
    FETCHING_BUY: "🔍 Fetching your buy orders...",
    FETCHING_SELL: "🔍 Fetching your sell orders...",
    NO_BUY_ORDERS: "📭 You don't have any buy orders yet.",
    NO_SELL_ORDERS: "📭 You don't have any sell orders yet.",
    BUY_ORDERS_TITLE: (count: number) => `🛒 Your Buy Orders (${count} total)`,
    SELL_ORDERS_TITLE: (count: number) =>
      `💰 Your Sell Orders (${count} total)`,
    ORDERS_READY_FOR_COMPLETION: (count: number) =>
      `🟠 Orders ready for completion: ${count}`,
    GIFTS_READY_FOR_DELIVERY: (count: number) =>
      `🎁 Gifts ready for delivery: ${count}`,
    SHOWING_LIMITED_ORDERS: "📝 Showing first 10 orders.",
    FETCH_ERROR_BUY: "❌ Failed to fetch your buy orders.",
    <PERSON>ET<PERSON>_ERROR_SELL: "❌ Failed to fetch your sell orders.",
  },

  BUSINESS_CONNECTION: {
    ORDER_NOT_FOUND: "❌ Order not found. Please try again.",
    INCORRECT_GIFT: "❌ You are sending an incorrect gift.",
    PROCESSING_GIFT: "🔄 Processing gift for relayer...",
    GIFT_SENT_SUCCESS: "✅ Gift sent to relayer!",
    GIFT_PROCESSING_ERROR: (orderId: string, error: string) =>
      `❌ Failed to process gift for order #${orderId}. Error: ${error}`,
    GIFT_PROCESSING_GENERIC_ERROR: (orderId: string) =>
      `❌ An error occurred while processing gift for order #${orderId}.`,
    NO_ORDER_FOR_PROCESSING: "❌ No order found for gift processing.",
    ALREADY_SENT_GIFT: "🎁 You have already sent a gift for this order.",
    GIFT_TRANSFERRED_SUCCESS: "🎁 Gift successfully transferred back to you!",
    GIFT_TRANSFER_ERROR: "❌ Failed to transfer gift.",
    GIFT_TRANSFER_MISSING_INFO: "❌ Unable to process gift transfer.",
    GIFT_TRANSFER_GENERIC_ERROR: "❌ Error transferring gift.",
    NO_GIFT_TO_TRANSFER: "❌ No gift to transfer.",
  },
  SUPPORT: {
    CONTACT_INFO: "📞 Contact Support - Email: <EMAIL>",
  },
  WELCOME: "🛍️ Welcome to the PREM Bot!",
  HELP: "🤖 PREM Bot Help",
} as const;

export const BUTTON_TEXTS = {
  MY_BUY_ORDERS: "🛒 My Buy Orders",
  MY_SELL_ORDERS: "💰 My Sell Orders",
  OPEN_MARKETPLACE: "🌐 Open Marketplace",
  CONTACT_SUPPORT: "📞 Contact Support",
  BACK_TO_ORDERS: "📋 Back to Orders",
  VIEW_ALL_ORDERS: "📋 View All Orders",
  VIEW_MY_ORDERS: "📋 View My Orders",
  CANCEL: "❌ Cancel",
} as const;
